"use client";
import {
  Book<PERSON>pen,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Lightbulb,
  LightbulbOff,
  Loader2,
  MessageSquare,
  MoreVertical,
  RefreshCw,
  RotateCcw,
  Trash2,
  X,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import AuthenticatedNavbar from "@/components/authenticated-navbar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import ConfirmationDialog from "@/components/ui/confirmation-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import NotepadModal from "@/components/ui/notepad-modal";
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation";
import { Textarea } from "@/components/ui/textarea";
import { trpc } from "@/utils/trpc";

// Type definitions
type ReplyGuyMention = {
  id: string;
  content: string;
  authorHandle: string;
  authorName: string;
  authorAvatar?: string | null;
  tweetUrl: string;
  platform: string;
  createdAt: Date | string;
  bullishScore?: number | null;
  keywords?: string[];
  hasAIResponse: boolean;
  account?: {
    id: string;
    handle: string;
    displayName?: string | null;
    avatar?: string | null;
    isActive: boolean;
  } | null;
  responses: Array<{
    id: string;
    content: string;
    model?: string | null;
    confidence?: number | null;
    tokensUsed?: number | null;
    createdAt: Date | string;
  }>;
};

type MonitoredAccount = {
  id: string;
  handle: string;
  displayName?: string;
  avatar?: string;
  isActive: boolean;
  platform: string;
  followerCount: number;
  createdAt: Date | string;
  lastSyncAt?: Date | null;
  mentionsCount: number;
};

// Utility functions for managing "don't show again" preference
const getDontShowDeleteConfirmationKey = () => {
  const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format
  return `hideDeleteConfirmation_${today}`;
};

const shouldHideDeleteConfirmation = (): boolean => {
  if (typeof window === "undefined") return false;
  try {
    const key = getDontShowDeleteConfirmationKey();
    return localStorage.getItem(key) === "true";
  } catch {
    return false;
  }
};

const setHideDeleteConfirmation = (hide: boolean): void => {
  if (typeof window === "undefined") return;
  try {
    const key = getDontShowDeleteConfirmationKey();
    if (hide) {
      localStorage.setItem(key, "true");
    } else {
      localStorage.removeItem(key);
    }
  } catch {
    // Silently fail if localStorage is not available
  }
};

export default function ReplyGuyPage() {
  const [showMonitoredAccounts, setShowMonitoredAccounts] = useState(false);
  const [selectedResponse, setSelectedResponse] = useState<
    Record<string, string>
  >({});
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {}
  );
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    responseId: string | null;
    mentionId: string | null;
  }>({
    isOpen: false,
    responseId: null,
    mentionId: null,
  });

  const [notepadModal, setNotepadModal] = useState<{
    isOpen: boolean;
    mentionId: string | null;
    mentionContent: string | null;
    authorHandle: string | null;
    authorName: string | null;
  }>({
    isOpen: false,
    mentionId: null,
    mentionContent: null,
    authorHandle: null,
    authorName: null,
  });

  const [dontShowDeleteConfirmation, setDontShowDeleteConfirmation] =
    useState(false);

  // tRPC queries and utils
  const utils = trpc.useUtils();
  const {
    data: mentions,
    isLoading: mentionsLoading,
    refetch: refetchMentions,
  } = trpc.mentions.getAll.useQuery({
    limit: 20,
    includeResponses: true,
  });
  const {
    data: monitoredAccounts,
    isLoading: accountsLoading,
    refetch: refetchAccounts,
  } = trpc.accounts.getMonitored.useQuery();

  // tRPC mutations
  const generateResponseMutation =
    trpc.benji.generateMentionResponse.useMutation();
  const enhanceMentionMutation = trpc.mentions.enhance.useMutation();
  const syncAllMutation = trpc.mentions.syncAllAccounts.useMutation();
  const syncAccountMutation = trpc.mentions.syncAccount.useMutation();
  const archiveMentionMutation = trpc.mentions.archive.useMutation();
  const deleteResponseMutation = trpc.mentions.deleteResponse.useMutation();

  const handleGenerateAIAnswer = async (mention: ReplyGuyMention) => {
    setLoadingStates((prev) => ({ ...prev, [`generate-${mention.id}`]: true }));
    try {
      const response = await generateResponseMutation.mutateAsync({
        mentionId: mention.id,
        mentionContent: mention.content,
        authorInfo: {
          name: mention.authorName,
          handle: mention.authorHandle,
        },
      });

      // Refetch mentions to get the new response
      await refetchMentions();
      toast.success("AI response generated successfully!");
    } catch (error) {
      console.error("Error generating AI response:", error);
      toast.error("Failed to generate AI response. Please try again.");
    } finally {
      setLoadingStates((prev) => ({
        ...prev,
        [`generate-${mention.id}`]: false,
      }));
    }
  };

  const handleEnhanceMention = async (mentionId: string) => {
    try {
      console.log("🚀 Frontend: Starting enhance for mention:", mentionId);
      const result = await enhanceMentionMutation.mutateAsync({ mentionId });
      console.log("✅ Frontend: Enhance completed:", result);

      // Invalidate and refetch mentions to ensure fresh data
      await utils.mentions.getAll.invalidate();

      // Small delay to ensure database transaction is committed
      await new Promise((resolve) => setTimeout(resolve, 500));

      const refetchResult = await refetchMentions();
      console.log(
        "🔄 Frontend: Refetch completed, mentions count:",
        refetchResult.data?.mentions?.length
      );

      // Find the updated mention to verify the new response was added
      const updatedMention = refetchResult.data?.mentions?.find(
        (m: ReplyGuyMention) => m.id === mentionId
      );
      console.log(
        "🔍 Frontend: Updated mention responses:",
        updatedMention?.responses?.length
      );

      toast.success("Enhanced AI response generated successfully!");
      console.log("Enhanced response details:", {
        id: result.response?.id,
        model: result.response?.model,
        contentLength: result.response?.content?.length,
      });
    } catch (error) {
      console.error("Error enhancing mention:", error);
      toast.error("Failed to generate enhanced response. Please try again.");
    }
  };

  const handleOpenNotepad = (mention: ReplyGuyMention) => {
    console.log("📝 Frontend: Opening notepad for mention:", mention.id);
    setNotepadModal({
      isOpen: true,
      mentionId: mention.id,
      mentionContent: mention.content,
      authorHandle: mention.authorHandle,
      authorName: mention.authorName,
    });
  };

  const handleCloseNotepad = () => {
    setNotepadModal({
      isOpen: false,
      mentionId: null,
      mentionContent: null,
      authorHandle: null,
      authorName: null,
    });
  };

  const handleDeleteMention = async (mentionId: string) => {
    try {
      // Archive the mention instead of permanently deleting it
      await archiveMentionMutation.mutateAsync({ mentionId });
      await refetchMentions();
      toast.success("Mention archived successfully!");
    } catch (error) {
      console.error("Error archiving mention:", error);
      toast.error("Failed to archive mention. Please try again.");
    }
  };

  const handleResponseChange = (mentionId: string, responseContent: string) => {
    setSelectedResponse((prev) => ({
      ...prev,
      [mentionId]: responseContent,
    }));
  };

  const handleReplyToMention = (tweetUrl: string) => {
    // Open the tweet in a new tab for replying
    window.open(tweetUrl, "_blank", "noopener,noreferrer");
  };

  const handleUseReply = async (mentionId: string, responseContent: string) => {
    // Open Twitter with pre-filled reply using URL parameters
    try {
      // Find the mention to get the tweet URL and extract tweet ID
      const mention = mentions?.mentions?.find((m: any) => m.id === mentionId);
      if (!mention) {
        toast.error("Could not find mention details.");
        return;
      }

      // Extract tweet ID from the URL
      // Twitter URLs are like: https://twitter.com/username/status/1234567890 or https://x.com/username/status/1234567890
      const tweetId =
        mention.tweetUrl.match(/\/status\/(\d+)/)?.[1] || mention.id;

      // Create Twitter compose URL with pre-filled reply
      // Format: https://twitter.com/intent/tweet?in_reply_to=TWEET_ID&text=RESPONSE_TEXT
      const encodedResponse = encodeURIComponent(responseContent);
      const replyUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodedResponse}`;

      // Open Twitter with pre-filled reply
      window.open(replyUrl, "_blank", "noopener,noreferrer");
      toast.success("Opening Twitter with pre-filled AI response!");
    } catch (error) {
      console.error("Failed to open Twitter with pre-filled reply:", error);
      // Fallback to copying to clipboard
      try {
        await navigator.clipboard.writeText(responseContent);
        const mention = mentions?.mentions?.find(
          (m: any) => m.id === mentionId
        );
        if (mention) {
          window.open(mention.tweetUrl, "_blank", "noopener,noreferrer");
        }
        toast.warning(
          "Opened Twitter normally. AI response copied to clipboard as fallback."
        );
      } catch (clipboardError) {
        toast.error(
          "Failed to open Twitter with pre-filled reply. Please copy manually."
        );
      }
    }
  };

  const handleRegenerateResponse = async (mention: ReplyGuyMention) => {
    setLoadingStates((prev) => ({
      ...prev,
      [`regenerate-${mention.id}`]: true,
    }));
    try {
      await generateResponseMutation.mutateAsync({
        mentionId: mention.id,
        mentionContent: mention.content,
        authorInfo: {
          name: mention.authorName,
          handle: mention.authorHandle,
        },
      });

      await refetchMentions();
      toast.success("Response regenerated successfully!");
    } catch (error) {
      console.error("Error regenerating response:", error);
      toast.error("Failed to regenerate response. Please try again.");
    } finally {
      setLoadingStates((prev) => ({
        ...prev,
        [`regenerate-${mention.id}`]: false,
      }));
    }
  };

  const handleDeleteResponse = (responseId: string, mentionId: string) => {
    // Check if user has chosen to hide confirmation for today
    if (shouldHideDeleteConfirmation()) {
      // Skip confirmation dialog and delete directly
      deleteResponseDirectly(responseId, mentionId);
    } else {
      // Show confirmation dialog
      setDeleteDialog({
        isOpen: true,
        responseId,
        mentionId,
      });
    }
  };

  const deleteResponseDirectly = async (
    responseId: string,
    mentionId: string
  ) => {
    setLoadingStates((prev) => ({ ...prev, [`delete-${responseId}`]: true }));

    try {
      await deleteResponseMutation.mutateAsync({ responseId });
      await refetchMentions();
      toast.success("AI response deleted successfully!");
    } catch (error) {
      console.error("Error deleting response:", error);
      toast.error("Failed to delete response. Please try again.");
    } finally {
      setLoadingStates((prev) => ({
        ...prev,
        [`delete-${responseId}`]: false,
      }));
    }
  };

  const confirmDeleteResponse = async () => {
    if (!deleteDialog.responseId || !deleteDialog.mentionId) return;

    const { responseId, mentionId } = deleteDialog;

    // Save the "don't show again" preference if checked
    if (dontShowDeleteConfirmation) {
      setHideDeleteConfirmation(true);
    }

    // Close dialog first
    closeDeleteDialog();

    // Then delete the response
    await deleteResponseDirectly(responseId, mentionId);
  };

  const handleDontShowAgainChange = (checked: boolean) => {
    setDontShowDeleteConfirmation(checked);
  };

  const closeDeleteDialog = () => {
    setDeleteDialog({ isOpen: false, responseId: null, mentionId: null });
    setDontShowDeleteConfirmation(false); // Reset checkbox state
  };

  const handleSyncAllMentions = async () => {
    try {
      const result = await syncAllMutation.mutateAsync();

      // Refetch mentions to show new data
      await refetchMentions();
      // Invalidate and refetch accounts to update sync timestamps
      await utils.accounts.getMonitored.invalidate();
      await refetchAccounts();

      if (result.success) {
        let message =
          result.message || `Synced ${result.totalNewMentions} new mentions!`;

        // Check if any accounts hit limits
        const limitReachedAccounts =
          result.accountResults?.filter((r: any) => r.limitReached) || [];
        if (limitReachedAccounts.length > 0) {
          message += ` (${limitReachedAccounts.length} account(s) reached limit)`;
        }

        toast.success(message);
      } else {
        toast.warning(`Sync completed with ${result.errors.length} errors`);
      }
    } catch (error) {
      console.error("Error syncing all mentions:", error);
      toast.error("Failed to sync mentions. Please try again.");
    }
  };

  const handleSyncAccount = async (accountId: string) => {
    try {
      const result = await syncAccountMutation.mutateAsync({ accountId });

      // Refetch mentions to show new data
      await refetchMentions();
      // Invalidate and refetch accounts to update sync timestamps
      await utils.accounts.getMonitored.invalidate();
      await refetchAccounts();

      toast.success(result.message || "Account synced successfully!");
    } catch (error) {
      console.error("Error syncing account:", error);
      toast.error("Failed to sync account. Please try again.");
    }
  };

  return (
    <div className="min-h-screen p-3 sm:p-4 md:p-6 lg:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-6 sm:mb-8">
        <h1 className="text-[clamp(1.75rem,5vw,3rem)] font-bold tracking-wider text-app-headline">
          REPLY GUY
        </h1>
      </header>
      <AuthenticatedNavbar currentPage="reply-guy" />

      <section className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4 items-stretch sm:items-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="border-app-stroke bg-app-card hover:bg-app-main hover:text-app-secondary text-app-headline min-h-[44px] px-4 text-sm sm:text-base w-full sm:w-auto"
                onClick={() => setShowMonitoredAccounts(!showMonitoredAccounts)}
              >
                <span className="truncate">Monitored Accounts</span>
                {showMonitoredAccounts ? (
                  <ChevronUp className="ml-2 h-4 w-4 flex-shrink-0" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4 flex-shrink-0" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 sm:w-64 bg-app-card border-app-stroke text-app-headline shadow-lg">
              <DropdownMenuLabel className="text-app-headline opacity-75 text-sm">
                Currently Monitored
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-app-stroke opacity-50" />
              {accountsLoading ? (
                <DropdownMenuItem
                  disabled
                  className="flex justify-center items-center py-4"
                >
                  <ShardLoadingAnimation size={48} />
                </DropdownMenuItem>
              ) : monitoredAccounts?.accounts?.length === 0 ? (
                <DropdownMenuItem
                  disabled
                  className="text-center opacity-60 py-4"
                >
                  No monitored accounts
                </DropdownMenuItem>
              ) : (
                monitoredAccounts?.accounts?.map((account: any) => (
                  <DropdownMenuItem
                    key={account.id}
                    className="flex justify-between items-center hover:bg-app-main hover:text-app-secondary focus:bg-app-main focus:text-app-secondary min-h-[44px] px-3"
                  >
                    <span className="truncate">@{account.handle}</span>
                    {account.isActive ? (
                      <Lightbulb className="w-4 h-4 text-app-main flex-shrink-0 ml-2" />
                    ) : (
                      <LightbulbOff className="w-4 h-4 text-app-highlight flex-shrink-0 ml-2" />
                    )}
                  </DropdownMenuItem>
                ))
              )}
              <DropdownMenuSeparator className="bg-app-stroke opacity-50" />
              <DropdownMenuItem className="hover:bg-app-main hover:text-app-secondary focus:bg-app-main focus:text-app-secondary min-h-[44px]">
                Manage Accounts...
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            onClick={handleSyncAllMentions}
            disabled={syncAllMutation.isPending || accountsLoading}
            className="bg-app-main text-app-secondary hover:bg-app-highlight disabled:opacity-50 min-h-[44px] px-4 text-sm sm:text-base w-full sm:w-auto"
          >
            {syncAllMutation.isPending ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                <span className="hidden sm:inline">Syncing...</span>
                <span className="sm:hidden">Sync...</span>
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Sync Mentions</span>
                <span className="sm:hidden">Sync</span>
              </>
            )}
          </Button>
        </div>
      </section>

      <main className="space-y-4 sm:space-y-6">
        {mentionsLoading ? (
          <div className="flex items-center justify-center py-8">
            <ShardLoadingAnimation size={64} />
          </div>
        ) : mentions?.mentions?.length === 0 ? (
          <div className="text-center py-10 text-app-headline opacity-60">
            <p className="text-base sm:text-lg">No mentions to display.</p>
            <p className="text-sm sm:text-base">
              Check back later or adjust your monitored accounts.
            </p>
          </div>
        ) : (
          mentions?.mentions?.map((mention: any) => (
            <Card
              key={mention.id}
              className="bg-app-card border-app-stroke text-app-headline rounded-lg shadow-md"
            >
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-base sm:text-lg text-app-headline truncate">
                      @{mention.authorHandle}
                    </CardTitle>
                    <p className="text-xs sm:text-sm text-app-headline opacity-70">
                      {new Date(mention.createdAt).toLocaleDateString()} via{" "}
                      {mention.platform}
                    </p>
                    <p className="text-sm text-app-headline opacity-80 truncate">
                      {mention.authorName}
                    </p>
                  </div>
                  {/* Mobile Layout - Empty for now, buttons moved to bottom */}
                  <div className="flex sm:hidden gap-2 justify-start"></div>

                  {/* Desktop Layout */}
                  <div className="hidden sm:flex flex-wrap gap-1 sm:gap-2 justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-app-main hover:text-app-highlight hover:bg-app-main/10 hover:scale-105 px-2 min-h-[44px] text-xs sm:text-sm transition-all duration-200 ease-in-out transform group"
                      onClick={() => handleReplyToMention(mention.tweetUrl)}
                    >
                      <MessageSquare className="w-4 h-4 mr-1 group-hover:animate-pulse" />
                      Reply
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-app-highlight hover:text-white hover:bg-red-500 hover:scale-105 px-2 min-h-[44px] text-xs sm:text-sm transition-all duration-200 ease-in-out transform"
                      onClick={() => handleDeleteMention(mention.id)}
                      disabled={archiveMentionMutation.isPending}
                    >
                      {archiveMentionMutation.isPending ? (
                        <Loader2 className="w-4 h-4 animate-spin mr-1" />
                      ) : (
                        <Trash2 className="w-4 h-4 mr-1" />
                      )}
                      <span className="hidden sm:inline">Delete</span>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm sm:text-base text-app-headline opacity-90 leading-relaxed mb-4">
                  {mention.content}
                </p>
                {mention.responses && mention.responses.length > 0 && (
                  <div className="mt-4 space-y-3">
                    {mention.responses.map((response: any, index: number) => (
                      <div
                        key={response.id}
                        className="p-3 sm:p-4 bg-app-background rounded-md border border-app-stroke"
                      >
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-3">
                          <div className="flex items-center gap-2">
                            <p className="text-sm sm:text-base font-semibold text-app-main">
                              AI Generated Response{" "}
                              {mention.responses.length > 1
                                ? `(${index + 1})`
                                : ""}
                            </p>
                            {response.model?.includes("enhanced") && (
                              <span className="inline-flex items-center gap-1 px-2 py-1 bg-app-main text-app-secondary text-xs rounded-full">
                                <Zap className="w-3 h-3" />
                                Enhanced (o3)
                              </span>
                            )}
                          </div>
                          {response.confidence && (
                            <span className="text-xs sm:text-sm text-app-headline opacity-70">
                              Confidence:{" "}
                              {Math.round(response.confidence * 100)}%
                            </span>
                          )}
                        </div>
                        <Textarea
                          value={
                            selectedResponse[mention.id] || response.content
                          }
                          onChange={(e) =>
                            handleResponseChange(mention.id, e.target.value)
                          }
                          className="bg-app-card border-app-stroke text-app-headline text-sm sm:text-base min-h-[80px] sm:min-h-[100px]"
                          rows={3}
                        />
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4 mt-3">
                          <div className="text-xs sm:text-sm text-app-headline opacity-60 text-right sm:text-left">
                            {response.model &&
                              `Model: ${response.model.includes("enhanced-benji") || response.model.includes("benji") ? "Benji" : response.model}`}
                            {response.used && (
                              <span className="ml-2 text-app-main">• Used</span>
                            )}
                          </div>
                          {/* Mobile Layout */}
                          <div className="flex sm:hidden items-center gap-2 justify-start">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary min-h-[44px] min-w-[44px] p-2"
                                >
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                align="start"
                                className="w-40 bg-app-card border-app-stroke text-app-headline"
                              >
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleRegenerateResponse(mention)
                                  }
                                  disabled={
                                    loadingStates[`regenerate-${mention.id}`]
                                  }
                                  className="text-app-headline hover:bg-app-main hover:text-app-secondary focus:bg-app-main focus:text-app-secondary min-h-[44px]"
                                >
                                  {loadingStates[`regenerate-${mention.id}`] ? (
                                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                  ) : (
                                    <RotateCcw className="w-4 h-4 mr-2" />
                                  )}
                                  Regenerate
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteResponse(
                                      response.id,
                                      mention.id
                                    )
                                  }
                                  disabled={
                                    loadingStates[`delete-${response.id}`]
                                  }
                                  className="text-app-headline hover:bg-red-500 hover:text-white focus:bg-red-500 focus:text-white min-h-[44px] transition-all duration-200 ease-in-out"
                                >
                                  {loadingStates[`delete-${response.id}`] ? (
                                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                  ) : (
                                    <X className="w-4 h-4 mr-2" />
                                  )}
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEnhanceMention(mention.id)}
                              disabled={enhanceMentionMutation.isPending}
                              className="border-app-stroke text-app-headline hover:bg-app-main hover:text-white hover:scale-105 hover:shadow-lg hover:border-app-main min-h-[44px] transition-all duration-200 ease-in-out transform group"
                            >
                              {enhanceMentionMutation.isPending ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                              ) : (
                                <Zap className="w-4 h-4 group-hover:animate-pulse" />
                              )}
                            </Button>
                            <Button
                              size="sm"
                              onClick={() =>
                                handleUseReply(
                                  mention.id,
                                  selectedResponse[mention.id] ||
                                    response.content
                                )
                              }
                              className="bg-app-main text-app-secondary hover:bg-app-highlight hover:scale-105 hover:shadow-lg min-h-[44px] transition-all duration-200 ease-in-out transform"
                            >
                              Use Reply
                            </Button>
                          </div>

                          {/* Desktop Layout */}
                          <div className="hidden sm:flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                handleDeleteResponse(response.id, mention.id)
                              }
                              disabled={loadingStates[`delete-${response.id}`]}
                              className="text-rose-400 border-rose-200 hover:text-white hover:bg-rose-500 hover:border-rose-500 hover:scale-105 px-3 py-1 min-h-[44px] text-xs sm:text-sm transition-all duration-200 ease-in-out transform"
                            >
                              {loadingStates[`delete-${response.id}`] ? (
                                <Loader2 className="w-4 h-4 animate-spin mr-1" />
                              ) : (
                                <X className="w-4 h-4 mr-1" />
                              )}
                              Delete
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRegenerateResponse(mention)}
                              disabled={
                                loadingStates[`regenerate-${mention.id}`]
                              }
                              className="border-app-stroke text-app-headline hover:bg-app-main hover:text-white hover:scale-105 hover:shadow-lg hover:border-app-main min-h-[44px] text-xs sm:text-sm transition-all duration-200 ease-in-out transform group"
                            >
                              {loadingStates[`regenerate-${mention.id}`] ? (
                                <Loader2 className="w-4 h-4 animate-spin mr-1" />
                              ) : (
                                <RotateCcw className="w-4 h-4 mr-1 group-hover:animate-spin" />
                              )}
                              Regenerate
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEnhanceMention(mention.id)}
                              disabled={enhanceMentionMutation.isPending}
                              className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary hover:scale-105 hover:shadow-lg hover:border-app-main min-h-[44px] text-xs sm:text-sm relative group transition-all duration-200 ease-in-out transform"
                              title="Generate enhanced response with OpenAI o3, AI tools, market intelligence, and real-time data"
                            >
                              {enhanceMentionMutation.isPending ? (
                                <Loader2 className="w-4 h-4 animate-spin mr-1" />
                              ) : (
                                <Zap className="w-4 h-4 mr-1 group-hover:animate-pulse" />
                              )}
                              Enhance
                              <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-app-card border border-app-stroke text-app-headline text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                o3 + AI Tools + Market Data
                              </span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleOpenNotepad(mention)}
                              className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary hover:scale-105 hover:shadow-lg hover:border-app-main min-h-[44px] text-xs sm:text-sm relative group transition-all duration-200 ease-in-out transform"
                              title="Open response notepad with research tools and draft management"
                            >
                              <BookOpen className="w-4 h-4 mr-1 group-hover:animate-bounce" />
                              Notepad
                              <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-app-card border border-app-stroke text-app-headline text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                Research + Sources + Drafts
                              </span>
                            </Button>
                            <Button
                              size="sm"
                              onClick={() =>
                                handleUseReply(
                                  mention.id,
                                  selectedResponse[mention.id] ||
                                    response.content
                                )
                              }
                              className="bg-app-main text-app-secondary hover:bg-app-highlight min-h-[44px] text-xs sm:text-sm px-4"
                            >
                              Use this reply
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex flex-col gap-3 pt-3 items-start">
                {mention.account && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-xs sm:text-sm text-app-headline opacity-60">
                    <span>via</span>
                    <span className="font-medium">
                      @{mention.account.handle}
                    </span>
                    {mention.account.displayName && (
                      <span className="opacity-80 truncate">
                        ({mention.account.displayName})
                      </span>
                    )}
                  </div>
                )}
                <div className="flex items-center gap-2 w-full">
                  <Button
                    onClick={() => handleGenerateAIAnswer(mention)}
                    disabled={loadingStates[`generate-${mention.id}`]}
                    className="bg-app-main text-app-secondary hover:bg-app-highlight hover:text-app-secondary min-h-[44px] flex-1 text-sm sm:text-base font-medium"
                  >
                    {loadingStates[`generate-${mention.id}`] ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        <span className="hidden sm:inline">Generating...</span>
                        <span className="sm:hidden">Generating...</span>
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        <span className="hidden sm:inline">
                          Generate AI Answer
                        </span>
                        <span className="sm:hidden">Generate AI</span>
                      </>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-app-main hover:text-app-highlight hover:bg-app-main/10 hover:scale-105 px-2 min-h-[44px] min-w-[44px] transition-all duration-200 ease-in-out transform group"
                    onClick={() => handleReplyToMention(mention.tweetUrl)}
                  >
                    <MessageSquare className="w-4 h-4 group-hover:animate-pulse" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-400 hover:text-white hover:bg-red-500 hover:scale-105 px-2 min-h-[44px] min-w-[44px] transition-all duration-200 ease-in-out transform"
                    onClick={() => handleDeleteMention(mention.id)}
                    disabled={archiveMentionMutation.isPending}
                  >
                    {archiveMentionMutation.isPending ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4" />
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleOpenNotepad(mention)}
                    className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary hover:scale-105 hover:shadow-lg hover:border-app-main min-h-[44px] transition-all duration-200 ease-in-out transform group"
                    title="Open response notepad with research tools"
                  >
                    <BookOpen className="w-4 h-4 group-hover:animate-bounce" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))
        )}
      </main>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={deleteDialog.isOpen}
        onClose={closeDeleteDialog}
        onConfirm={confirmDeleteResponse}
        title="Delete AI Response"
        message="Are you sure you want to delete this AI response? Think ultra carefully about this decision. This action cannot be undone and the response will be permanently removed."
        confirmText="Yes, Delete"
        cancelText="Cancel"
        confirmVariant="default"
        showDontShowAgain={true}
        dontShowAgainChecked={dontShowDeleteConfirmation}
        onDontShowAgainChange={handleDontShowAgainChange}
      />

      {/* Notepad Modal */}
      {notepadModal.isOpen && notepadModal.mentionId && (
        <NotepadModal
          isOpen={notepadModal.isOpen}
          onClose={handleCloseNotepad}
          mentionId={notepadModal.mentionId}
          mentionContent={notepadModal.mentionContent || ""}
          authorHandle={notepadModal.authorHandle || ""}
          authorName={notepadModal.authorName || ""}
        />
      )}
    </div>
  );
}
